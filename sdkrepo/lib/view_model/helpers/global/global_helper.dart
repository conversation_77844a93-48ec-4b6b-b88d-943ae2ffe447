import 'dart:math';
import 'dart:io';
import 'dart:typed_data';
import 'package:path_provider/path_provider.dart';
import 'package:encrypt/encrypt.dart';
import '../../../resources/exports/index.dart';

class GlobalHelper {
  static Set<int> setOfInts = {};

  static Future<String> compressImageToBase64(XFile imageFile) async {
    try {
      Uint8List? compressedImage = await FlutterImageCompress.compressWithList(
        await imageFile.readAsBytes(),
        quality: 65,
      );
      String base64String = base64Encode(compressedImage);
      return base64String;
    } catch (error) {
      log.f('Compression error: $error');
      return "";
    }
  }

  static Future<String> imageToBase64(Uint8List imageFile) async {
    try {
      Uint8List? compressedImage = await FlutterImageCompress.compressWithList(
        imageFile,
        quality: 65,
      );
      String base64String = base64Encode(compressedImage);
      return base64String;
    } catch (error) {
      log.f('Compression error: $error');
      return "";
    }
  }

  static void showPopupLoader() {
    showDialog(
      context: Get.context!,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                color: Colors.black,
              ),
              SizedBox(height: 16),
              Text('Please Wait...'),
            ],
          ),
        );
      },
    );
  }

  static Future<String> xfileToBase64(XFile? file) async {
    if (file == null) {
      CustomSnackBar.errorSnackBar(message: Strings.FILE_IS_EMPTY);
      return '';
    }
    Uint8List unit8List = await file.readAsBytes();
    List<int> imageBytes = List.from(unit8List);
    String base64Image = base64Encode(imageBytes);
    return base64Image;
  }

  static int getRandomId() {
    int value = Random().nextInt(99999999);
    while (setOfInts.contains(value)) {
      value = Random().nextInt(99999999);
    }
    setOfInts.add(value);
    return setOfInts.last;
  }

  static double getDiscountPice(double price, int disPer) {
    double disPrice = (disPer / 100) * price;
    if (disPrice.isInfinite || disPrice.isNegative) return 0.0;
    return disPrice;
  }

  static List<String> alphabets = [
    'a',
    'b',
    'c',
    'd',
    'e',
    'f',
    'g',
    'h',
    'i',
    'j',
    'k',
    'l',
    'm',
    'n',
    'o',
    'p',
    'q',
    'r',
    's',
    't',
    'u',
    'v',
    'w',
    'x',
    'y',
    'z',
  ];

  Future<XFile> getFileFromUint8List(Uint8List data, String fileName) async {
    // Get the temporary directory of the device
    final tempDir = await getTemporaryDirectory();

    // Create a file path
    final filePath = '${tempDir.path}/$fileName.png';

    // Write the Uint8List data to the file
    final file = File(filePath);
    await file.writeAsBytes(data);

    XFile xfile = XFile(file.path);
    return xfile;
  }

  static String encryptData(Map<String, String> data, String publicKeyPem) {
    try {
      final parser = RSAKeyParser();
      final publicKey = parser.parse(publicKeyPem);
      final encrypter = Encrypter(RSA(publicKey: publicKey as dynamic));
      final jsonString = jsonEncode(data);
      final encrypted = encrypter.encrypt(jsonString);
      return encrypted.base64;
    } catch (e) {
      log.e('Encryption error: $e');
      return '';
    }
  }
}
