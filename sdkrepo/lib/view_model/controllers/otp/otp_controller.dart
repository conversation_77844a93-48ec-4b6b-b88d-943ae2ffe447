import 'package:digital_onboarding/digital_onboarding.dart';
import 'package:http/http.dart' as http;

import '../../../resources/exports/index.dart';

class OtpController extends GetxController {
  Timer? otpTimer;
  Duration myDuration = const Duration(minutes: 5);
  bool isLoading = false;

  late SessionOtpModel otpModel;
  late String type;
  bool isSignup = false;
  String? signupToken;
  String? sessionId;

  late TextEditingController pinCtrl;

  Future<void> updateSession() async {
    try {
      if (isSignup) {
        // Handle signup OTP verification
        final bool isVerified = await _verifySignupOtp();
        if (!isVerified) {
          return CustomSnackBar.errorSnackBar(
            message: Strings.SOMETHING_WENT_WRONG,
          );
        }
      } else {
        // Handle login OTP verification
        final bool isVerified = await DigitalOnboardingServices.verifyOtp(
            loginType: otpModel.email == null ? DigitalOnBoardingLoginType.phone : DigitalOnBoardingLoginType.email, value: otpModel.email ?? otpModel.phone ?? "", otp: pinCtrl.text);

        if (!isVerified) {
          return CustomSnackBar.errorSnackBar(
            message: Strings.SOMETHING_WENT_WRONG,
          );
        }
      }

      Get.offAllNamed(Routes.DASHBOARD);
    } on DigitalOnboardingException catch (e) {
      return CustomSnackBar.errorSnackBar(
        message: e.message ?? Strings.SOMETHING_WENT_WRONG,
      );
    } catch (e) {
      return CustomSnackBar.errorSnackBar(
        message: Strings.SOMETHING_WENT_WRONG,
      );
    }
  }

  Future<bool> _verifySignupOtp() async {
    try {
      final headers = {
        'Accept-Language': 'en',
        'Authorization': 'Bearer $signupToken',
      };

      final requestBody = {
        'step': 'login',
        'type': otpModel.email != null ? 'email' : 'phone',
        'email_or_phone': otpModel.email ?? otpModel.phone ?? '',
        'otp': pinCtrl.text,
      };

      final request = http.MultipartRequest(
        'POST',
        Uri.parse('https://onboarding-middleware.rndlabs.dev/api/v1/update_session'),
      );

      request.fields.addAll(requestBody.map((key, value) => MapEntry(key, value.toString())));
      request.headers.addAll(headers);

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        return responseData['success'] == true;
      } else {
        log.e('Signup OTP verification failed: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      log.e('Signup OTP verification error: $e');
      return false;
    }
  }

  void startTimer() {
    update(["resend_otp"]);
    otpTimer = Timer.periodic(
      const Duration(seconds: 1),
      (_) {
        final seconds = myDuration.inSeconds - 1;
        if (seconds == -1) {
          otpTimer?.cancel();
          update(["confirm_otp_button"]);
        } else {
          myDuration = Duration(seconds: seconds);
        }
        update(["otp_timer_text"]);
      },
    );
  }

  String getOtpDuration() {
    String strDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = strDigits(myDuration.inMinutes.remainder(60));
    final seconds = strDigits(myDuration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  void onInit() {
    otpModel = Get.arguments != null ? Get.arguments['otpModel'] : SessionOtpModel();

    // Check if this is a signup flow
    if (Get.arguments != null) {
      isSignup = Get.arguments['isSignup'] ?? false;
      signupToken = Get.arguments['signupToken'];
      sessionId = Get.arguments['sessionId'];
    }

    pinCtrl = TextEditingController();

    startTimer();
    super.onInit();
  }

  @override
  void dispose() {
    pinCtrl.dispose();
    otpTimer?.cancel();
    super.dispose();
  }
}
