import 'package:intl_phone_field/countries.dart';
import '../../../resources/exports/index.dart';
import 'package:http/http.dart' as http;

enum SignupType { email, phone }

class SignupController extends GetxController {
  late TextEditingController usernameCtrl;
  late TextEditingController emailCtrl;
  late TextEditingController phoneCtrl;
  late TextEditingController passwordCtrl;
  late TextEditingController confirmPasswordCtrl;

  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  Country? countryCode;
  RxBool isEmailType = true.obs;
  RxBool isLoading = false.obs;

  void toggleInputType() {
    isEmailType.value = !isEmailType.value;
    // Clear the opposite field when switching
    if (isEmailType.value) {
      phoneCtrl.clear();
    } else {
      emailCtrl.clear();
    }
  }

  String? validateUsername(String? value) {
    if (value == null || value.isEmpty) {
      return 'Username is required';
    }
    if (value.length < 3) {
      return 'Username must be at least 3 characters';
    }
    return null;
  }

  String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  String? validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }
    if (value != passwordCtrl.text) {
      return 'Passwords do not match';
    }
    return null;
  }

  String? validateEmailOrPhone() {
    if (isEmailType.value) {
      return Validators.emailValidation(emailCtrl.text);
    } else {
      if (phoneCtrl.text.isEmpty) {
        return 'Phone number is required';
      }
      return null;
    }
  }

  Future<void> signup() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    final emailOrPhoneValidation = validateEmailOrPhone();
    if (emailOrPhoneValidation != null) {
      CustomSnackBar.errorSnackBar(message: emailOrPhoneValidation);
      return;
    }

    isLoading.value = true;
    update(['signup_button']);

    try {
      // Prepare the data to encrypt
      final dataToEncrypt = {
        'username': usernameCtrl.text,
        'password': passwordCtrl.text,
      };

      // Encrypt the data
      final encryptedData = GlobalHelper.encryptData(dataToEncrypt, publicPem);

      if (encryptedData.isEmpty) {
        CustomSnackBar.errorSnackBar(message: 'Failed to encrypt data');
        isLoading.value = false;
        update(['signup_button']);
        return;
      }

      // Prepare the request body
      final String emailOrPhone = isEmailType.value ? emailCtrl.text : "${countryCode?.dialCode ?? "+964"}${phoneCtrl.text}";

      final requestBody = {
        'data': encryptedData,
        'type': isEmailType.value ? 'email' : 'phone',
        'email_or_phone': emailOrPhone,
      };

      // Make the API call
      final response = await _makeSignupRequest(requestBody);

      if (response != null && response['success'] == true) {
        final data = response['data'];
        final token = data['token'];
        final sessionId = data['session_id'];

        // Navigate to OTP screen with signup context
        Get.toNamed(Routes.OTP, arguments: {
          'otpModel': SessionOtpModel(
            email: isEmailType.value ? emailOrPhone : null,
            phone: !isEmailType.value ? emailOrPhone : null,
          ),
          'isSignup': true,
          'signupToken': token,
          'sessionId': sessionId,
        });
      } else {
        CustomSnackBar.errorSnackBar(message: response?['message'] ?? 'Signup failed');
      }
    } catch (e) {
      log.e('Signup error: $e');
      CustomSnackBar.errorSnackBar(message: 'Something went wrong');
    } finally {
      isLoading.value = false;
      update(['signup_button']);
    }
  }

  Future<Map<String, dynamic>?> _makeSignupRequest(Map<String, dynamic> body) async {
    try {
      final headers = <String, String>{
        'Accept-Language': 'en',
        'Key': FlutterConfig.get("appId") ?? 'Nb5nl-bwNlP-NNMdO-hL7Oe-VEHIk-JwM2s-5qs2I',
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ${AuthManager.instance.token}',
      };

      final response = await http
          .post(
            Uri.parse('https://onboarding-middleware.rndlabs.dev/api/v1/signup'),
            headers: headers,
            body: jsonEncode(body),
          )
          .timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        log.e('Signup API error: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      log.e('Signup request error: $e');
      return null;
    }
  }

  @override
  void onInit() {
    usernameCtrl = TextEditingController();
    emailCtrl = TextEditingController();
    phoneCtrl = TextEditingController();
    passwordCtrl = TextEditingController();
    confirmPasswordCtrl = TextEditingController();
    countryCode = countries.firstWhere((country) => country.code == 'IQ');
    super.onInit();
  }

  @override
  void dispose() {
    usernameCtrl.dispose();
    emailCtrl.dispose();
    phoneCtrl.dispose();
    passwordCtrl.dispose();
    confirmPasswordCtrl.dispose();
    super.dispose();
  }
}
