import '../../resources/exports/index.dart';

class Login extends GetView<LoginController> {
  const Login({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: const CustomAppBar(title: Strings.EMAIL_VRIFICATION, backallow: true),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomTabBar(
                tab: const [
                  Text(Strings.EMAIL),
                  Text(Strings.PHONE),
                ],
                tabCtrl: controller.tabCtrl,
                children: const [
                  LoginEmailForm(),
                  LoginMobileForm(),
                ],
              ).constrainedBox(maxHeight: 350),
              const SpaceH20(),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "Don't have an account? ",
                    style: context.bodyMedium,
                  ),
                  TextButton(
                    onPressed: () => Get.toNamed(Routes.SIGNUP),
                    child: Text(
                      'Sign Up',
                      style: context.bodyMedium.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
