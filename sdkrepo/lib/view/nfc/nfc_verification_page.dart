import 'package:flutter/cupertino.dart';

import '../../resources/exports/index.dart';
import '../../view_model/services/nfc/nfc_scanning_service.dart';

class NFCVerificationPage extends GetView<NFCController> {
  const NFCVerificationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: const CustomAppBar(
        title: "Scan NFC Chip",
        backallow: true,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // Information Section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              clipBehavior: Clip.antiAlias,
              decoration: ShapeDecoration(
                color: const Color(0xffE9EFF1),
                shape: ContinuousRectangleBorder(
                  borderRadius: BorderRadius.circular(32.0),
                ),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Ready for NFC Scanning",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  SpaceH8(),
                  Text(
                    "Your document details are ready. Now place your document on the back of your phone to scan the NFC chip for verification.",
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.black,
                    ),
                  ),
                ],
              ),
            ),

            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Personal Information Section
                Padding(
                  padding: const EdgeInsets.only(left: 20, bottom: 4),
                  child: Text(
                    "DOCUMENT INFORMATION",
                    style: context.bodyMedium.copyWith(
                      fontWeight: FontWeight.w300,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ),
                Container(
                  margin: const EdgeInsets.only(bottom: 32),
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  clipBehavior: Clip.antiAlias,
                  decoration: ShapeDecoration(
                    color: const Color(0xffE9EFF1),
                    shape: ContinuousRectangleBorder(
                      borderRadius: BorderRadius.circular(24),
                    ),
                  ),
                  child: GetBuilder<NFCController>(
                    id: 'mrz_data_display',
                    builder: (_) {
                      return Column(
                        children: [
                          _buildDataField(context, "Gender", controller.selectedGender == 'M' ? 'Male' : 'Female'),
                          _buildDataField(context, "Country", controller.countryCodeController.text),
                          _buildDataField(context, "Date of Birth", controller.dateOfBirthController.text),
                          _buildDataField(context, "Expiry Date", controller.expiryDateController.text),
                          _buildDataField(context, "Document Number", controller.documentNumberController.text, isNotLast: false),
                        ],
                      );
                    },
                  ),
                ),
              ],
            ),

            // NFC Scanner Section
            SizedBox(
              height: 0,
              child: NFCScanningService.buildMRZScanner(
                onMessage: (textData) async {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text(textData)),
                  );
                },
                onMrz: (mrzResult) async {
                  // This won't be called in NFC verification mode
                  // but we need to provide it for the scanner
                },
                onScan: (mrzData, imagedata) async {
                  if (mrzData != null) {
                    final combinedData = controller.getCombinedMRZData(mrzData);
                    await controller.onVerify(combinedData, imagedata);
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text("NFC Data Empty"),
                      ),
                    );
                  }
                },
              ),
            ),

            // Action Buttons
            Column(
              children: [
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () async {
                    NFCScanningService.startScanning();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text("Place your document on the back of your phone"),
                        duration: Duration(seconds: 3),
                      ),
                    );
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    width: double.maxFinite,
                    clipBehavior: Clip.antiAlias,
                    decoration: ShapeDecoration(
                      color: const Color(0xff8240DE),
                      shape: ContinuousRectangleBorder(
                        borderRadius: BorderRadius.circular(24.0),
                      ),
                    ),
                    alignment: Alignment.center,
                    child: const DefaultTextStyle(
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      child: Text("Start NFC Chip Scanning"),
                    ),
                  ),
                ),
                const SpaceH16(),
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    Get.back();
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 15),
                    width: double.maxFinite,
                    clipBehavior: Clip.antiAlias,
                    decoration: ShapeDecoration(
                      shape: ContinuousRectangleBorder(
                        borderRadius: BorderRadius.circular(24.0),
                        side: const BorderSide(color: Color(0xff8240DE)),
                      ),
                    ),
                    alignment: Alignment.center,
                    child: const DefaultTextStyle(
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      child: Text(
                        "Edit Document Details",
                        style: TextStyle(
                          color: Color(0xff8240DE),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataField(BuildContext context, String label, String? value, {bool? isNotLast}) {
    return Padding(
      padding: const EdgeInsets.only(left: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const SpaceH12(),
          Text(
            label,
            style: context.bodySmall.copyWith(
              color: Colors.black54,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          if (value == null || value.isEmpty) ...[
            Text(
              'Not available',
              style: context.bodyLarge.copyWith(
                color: Colors.grey[500],
                fontWeight: FontWeight.w600,
              ),
            ),
          ] else ...[
            Text(
              value,
              style: context.bodyLarge.copyWith(
                color: Colors.black87,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
          const SizedBox(height: 12),
          if (isNotLast ?? true)
            Divider(
              height: 1.5,
              color: Colors.grey[300],
            ),
        ],
      ),
    );
  }
}
