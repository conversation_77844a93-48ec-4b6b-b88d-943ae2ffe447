import '../../resources/exports/index.dart';
import '../../view_model/services/nfc/nfc_scanning_service.dart';

class NFCScreen extends GetView<NFCController> {
  const NFCScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.black,
      body: Stack(
        children: [
          // Full Screen MRZ Scanner
          Positioned.fill(
            child: NFCScanningService.buildMRZScanner(
              onMessage: (textData) async {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(textData)),
                );
              },
              onMrz: (mrzResult) async {
                // Auto-fill the form fields with scanned data
                controller.nameController.text = mrzResult.givenNames.toString();
                controller.selectedGender = mrzResult.sex.name == 'male' ? 'M' : 'F';
                controller.countryCodeController.text = mrzResult.countryCode.toString();
                controller.dateOfBirthController.text = mrzResult.birthDate.toString();
                controller.expiryDateController.text = mrzResult.expiryDate.toString();
                controller.documentNumberController.text = mrzResult.documentNumber.toString();

                // Navigate directly to manual form to verify/edit the scanned data
                Get.toNamed(Routes.MANUAL_MRZ_FORM);
              },
              onScan: (mrzData, imagedata) async {
                // This is for NFC scanning, not needed in MRZ scanning phase
              },
            ),
          ),

          // Overlay Button at Bottom
          Positioned(
            bottom: 50,
            left: 20,
            right: 20,
            child: CustomButton.outline(
              borderColor: AppColors.white,
              textColor: AppColors.white,
              text: "Enter Details Manually",
              onTapAsync: () async {
                // Clear any auto-filled data and go to manual form
                controller.clearFormData();
                Get.toNamed(Routes.MANUAL_MRZ_FORM);
              },
              radius: Sizes.RADIUS_12,
              constraints: const BoxConstraints(minHeight: 55),
            ),
          ),
        ],
      ),
    );
  }
}
