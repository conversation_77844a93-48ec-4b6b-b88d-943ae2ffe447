import 'package:flutter/cupertino.dart';

import '../../resources/exports/index.dart';

class ManualMRZForm extends GetView<NFCController> {
  const ManualMRZForm({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: const CustomAppBar(
        title: "Verify Document Details",
        backallow: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              "Please verify and edit the document details if needed:",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: AppColors.black,
              ),
            ),
            const SpaceH20(),

            // Gender Field
            GetBuilder<NFCController>(
              id: 'gender_dropdown',
              builder: (_) {
                return DropdownButtonFormField<String>(
                  value: controller.selectedGender,
                  decoration: InputDecoration(
                    labelText: "Gender",
                    labelStyle: const TextStyle(color: AppColors.black),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: AppColors.greyShade3),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: AppColors.primary),
                    ),
                  ),
                  items: ['M', 'F'].map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value == 'M' ? 'Male' : 'Female'),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    controller.updateGender(newValue);
                  },
                );
              },
            ),
            const SpaceH16(),

            // Country Code Field
            CustomTextFormField(
              controller: controller.countryCodeController,
              isRequired: true,
              height: Sizes.HEIGHT_20,
              labelText: "Country Code (e.g., IRQ, USA, GBR)",
              labelColor: AppColors.black,
              textColor: AppColors.black,
              cursorColor: AppColors.black,
              errorColor: AppColors.error,
              enableBorderColor: AppColors.greyShade3,
              focusBorderColor: AppColors.primary,
              textInputAction: TextInputAction.next,
              keyboardType: TextInputType.text,
            ),
            const SpaceH16(),

            // Date of Birth Field
            GestureDetector(
              onTap: () => controller.selectDateOfBirth(context),
              child: AbsorbPointer(
                child: CustomTextFormField(
                  controller: controller.dateOfBirthController,
                  isRequired: true,
                  height: Sizes.HEIGHT_20,
                  labelText: "Date of Birth",
                  labelColor: AppColors.black,
                  textColor: AppColors.black,
                  cursorColor: AppColors.black,
                  errorColor: AppColors.error,
                  enableBorderColor: AppColors.greyShade3,
                  focusBorderColor: AppColors.primary,
                  textInputAction: TextInputAction.next,
                  keyboardType: TextInputType.datetime,
                  suffixIcon: Icons.calendar_today,
                ),
              ),
            ),
            const SpaceH16(),

            // Expiry Date Field
            GestureDetector(
              onTap: () => controller.selectExpiryDate(context),
              child: AbsorbPointer(
                child: CustomTextFormField(
                  controller: controller.expiryDateController,
                  isRequired: true,
                  height: Sizes.HEIGHT_20,
                  labelText: "Expiry Date",
                  labelColor: AppColors.black,
                  textColor: AppColors.black,
                  cursorColor: AppColors.black,
                  errorColor: AppColors.error,
                  enableBorderColor: AppColors.greyShade3,
                  focusBorderColor: AppColors.primary,
                  textInputAction: TextInputAction.next,
                  keyboardType: TextInputType.datetime,
                  suffixIcon: Icons.calendar_today,
                ),
              ),
            ),
            const SpaceH16(),

            // Document Number Field
            CustomTextFormField(
              controller: controller.documentNumberController,
              isRequired: true,
              height: Sizes.HEIGHT_20,
              labelText: "Document Number",
              labelColor: AppColors.black,
              textColor: AppColors.black,
              cursorColor: AppColors.black,
              errorColor: AppColors.error,
              enableBorderColor: AppColors.greyShade3,
              focusBorderColor: AppColors.primary,
              textInputAction: TextInputAction.done,
              keyboardType: TextInputType.text,
            ),
            const SpaceH32(),

            // Action Buttons
            GetBuilder<NFCController>(
              id: 'submit_button',
              builder: (_) {
                return Column(
                  children: [
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () async {
                        await controller.proceedToNFCVerification();
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        width: double.maxFinite,
                        clipBehavior: Clip.antiAlias,
                        decoration: ShapeDecoration(
                          color: const Color(0xff8240DE),
                          shape: ContinuousRectangleBorder(
                            borderRadius: BorderRadius.circular(24.0),
                          ),
                        ),
                        alignment: Alignment.center,
                        child: const DefaultTextStyle(
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                          child: Text("Proceed to NFC Verification"),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
