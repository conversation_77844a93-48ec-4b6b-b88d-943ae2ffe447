import 'package:example/view/address/address_page.dart';
import 'package:example/view/doc_verification/document_type_selection.dart';
import 'package:example/view/extra_documents/extra_documents.dart';
import 'package:example/view/merchant_login_screen/merchant_login.dart';
import 'package:example/view_model/bindings/address/address_binding.dart';
import 'package:example/view_model/bindings/extra_documents/extra_documents_binidng.dart';

import '../exports/index.dart';

class Pages {
  static Transition get _routeTransition => Transition.rightToLeft;

  static Route<dynamic>? onGenerateRoute(RouteSettings settings) {
    Get.routing.args = settings.arguments;
    switch (settings.name) {
      case Routes.OFFLINE:
        return GetPageRoute(
          settings: settings,
          page: () => const OfflineScreen(),
          transition: _routeTransition,
        );
      case Routes.LOGIN:
        return GetPageRoute(
          settings: settings,
          page: () => const Login(),
          binding: LoginBinding(),
          transition: _routeTransition,
        );
      case Routes.SIGNUP:
        return GetPageRoute(
          settings: settings,
          page: () => const Signup(),
          binding: SignupBinding(),
          transition: _routeTransition,
        );
      case Routes.HOME:
        return GetPageRoute(
          settings: settings,
          page: () => const MerchantLogin(),
          binding: HomeBinding(),
          transition: _routeTransition,
        );
      case Routes.PHOTOVIEW:
        return GetPageRoute(
          settings: settings,
          page: () => const PhotoViewScreen(),
          transition: _routeTransition,
        );
      case Routes.OTP:
        return GetPageRoute(
          settings: settings,
          page: () => const Otp(),
          binding: OtpBinding(),
          transition: _routeTransition,
        );
      case Routes.DASHBOARD:
        return GetPageRoute(
          settings: settings,
          page: () => const Dashboard(),
          binding: DashboardBinding(),
          transition: _routeTransition,
        );
      case Routes.DOC_VERIFICATION:
        return GetPageRoute(
          settings: settings,
          page: () => const DocumentTypeSelectionWidget(),
          binding: DocVerificationBinding(),
          transition: _routeTransition,
        );
      case Routes.QUESTIONS:
        return GetPageRoute(
          settings: settings,
          page: () => const QAScreen(),
          binding: QABinding(),
          transition: _routeTransition,
        );
      case Routes.SIGNATURE:
        return GetPageRoute(
          settings: settings,
          page: () => const SignatureScreen(),
          binding: SignatureBinding(),
          transition: _routeTransition,
        );
      case Routes.SELFIE_VERIFICATION:
        return GetPageRoute(
          settings: settings,
          page: () => const SelfieVerification(),
          binding: SelfieVerificationBinding(),
          transition: _routeTransition,
        );
      case Routes.NFC:
        return GetPageRoute(
          settings: settings,
          page: () => const NFCScreen(),
          binding: NFCBinding(),
          transition: _routeTransition,
        );
      case Routes.NFC_DETAILS:
        return GetPageRoute(
          settings: settings,
          page: () => const NFCDetailsPage(),
          binding: DashboardBinding(),
          transition: _routeTransition,
        );
      case Routes.EXTRA_DOCUMENTS:
        return GetPageRoute(
          settings: settings,
          page: () => const ExtraDocuments(),
          binding: ExtraDocBinding(),
          transition: _routeTransition,
        );
      case Routes.ADDRESS_PAGE:
        return GetPageRoute(
          settings: settings,
          page: () => AddressPage(),
          binding: AddressBinding(),
          transition: _routeTransition,
        );
      case Routes.MANUAL_MRZ_FORM:
        return GetPageRoute(
          settings: settings,
          page: () => const ManualMRZForm(),
          binding: NFCBinding(),
          transition: _routeTransition,
        );
      case Routes.NFC_VERIFICATION:
        return GetPageRoute(
          settings: settings,
          page: () => const NFCVerificationPage(),
          binding: NFCBinding(),
          transition: _routeTransition,
        );

      default:
        return null;
    }
  }
}
